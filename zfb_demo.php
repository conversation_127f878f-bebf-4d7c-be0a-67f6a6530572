<?php
/**
 * 支付宝账单查询演示脚本
 * 查询 2025-05-30 的交易数据
 */

echo "=== 支付宝账单查询系统 ===\n";
echo "查询日期: 2025-05-30\n";
echo "查询时间: " . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

// 模拟查询参数
$queryParams = [
    'start_time' => '2025-05-30 00:00:00',
    'end_time' => '2025-05-31 00:00:00',
    'page_no' => '1',
    'page_size' => '2000',
    'app_id' => '2021005119698187'
];

echo "查询参数:\n";
foreach ($queryParams as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 模拟API响应数据（基于readme.mz中的示例格式）
$mockResponse = [
    "page_no" => "1",
    "page_size" => "2000", 
    "total_size" => "15",
    "detail_list" => [
        [
            "gmt_create" => "2025-05-30 08:30:15",
            "gmt_pay" => "2025-05-30 08:30:18",
            "gmt_refund" => "",
            "alipay_order_no" => "2025053088001234567890123456",
            "merchant_order_no" => "TX20250530001",
            "other_account" => "张*(z***@alipay.com)",
            "goods_title" => "在线商城购物",
            "total_amount" => "299.00",
            "net_mdiscount" => "10.00",
            "refund_amount" => "0.00",
            "service_fee" => "2.99",
            "trade_status" => "交易成功",
            "trade_type" => "即时到账",
            "store_no" => "Store001",
            "store_name" => "线上旗舰店",
            "goods_memo" => "手机数码产品"
        ],
        [
            "gmt_create" => "2025-05-30 10:15:32",
            "gmt_pay" => "2025-05-30 10:15:35",
            "gmt_refund" => "",
            "alipay_order_no" => "2025053088001234567890123457",
            "merchant_order_no" => "TX20250530002",
            "other_account" => "李*(l***@alipay.com)",
            "goods_title" => "餐饮外卖订单",
            "total_amount" => "68.50",
            "net_mdiscount" => "5.00",
            "refund_amount" => "0.00",
            "service_fee" => "0.69",
            "trade_status" => "交易成功",
            "trade_type" => "即时到账",
            "store_no" => "Store002",
            "store_name" => "美食广场",
            "goods_memo" => "午餐套餐"
        ],
        [
            "gmt_create" => "2025-05-30 14:22:18",
            "gmt_pay" => "2025-05-30 14:22:20",
            "gmt_refund" => "2025-05-30 16:30:00",
            "alipay_order_no" => "2025053088001234567890123458",
            "merchant_order_no" => "TX20250530003",
            "other_account" => "王*(w***@alipay.com)",
            "goods_title" => "服装购买",
            "total_amount" => "158.00",
            "net_mdiscount" => "0.00",
            "refund_amount" => "158.00",
            "service_fee" => "1.58",
            "trade_status" => "交易关闭",
            "trade_type" => "即时到账",
            "store_no" => "Store003",
            "store_name" => "时尚服饰店",
            "goods_memo" => "春季新款上衣"
        ],
        [
            "gmt_create" => "2025-05-30 16:45:12",
            "gmt_pay" => "2025-05-30 16:45:15",
            "gmt_refund" => "",
            "alipay_order_no" => "2025053088001234567890123459",
            "merchant_order_no" => "TX20250530004",
            "other_account" => "陈*(c***@alipay.com)",
            "goods_title" => "生活用品",
            "total_amount" => "89.90",
            "net_mdiscount" => "3.00",
            "refund_amount" => "0.00",
            "service_fee" => "0.90",
            "trade_status" => "交易成功",
            "trade_type" => "即时到账",
            "store_no" => "Store004",
            "store_name" => "日用百货",
            "goods_memo" => "洗护用品套装"
        ],
        [
            "gmt_create" => "2025-05-30 19:30:45",
            "gmt_pay" => "2025-05-30 19:30:48",
            "gmt_refund" => "",
            "alipay_order_no" => "2025053088001234567890123460",
            "merchant_order_no" => "TX20250530005",
            "other_account" => "刘*(l***@alipay.com)",
            "goods_title" => "电影票购买",
            "total_amount" => "45.00",
            "net_mdiscount" => "0.00",
            "refund_amount" => "0.00",
            "service_fee" => "0.45",
            "trade_status" => "交易成功",
            "trade_type" => "即时到账",
            "store_no" => "Store005",
            "store_name" => "影城票务",
            "goods_memo" => "晚场电影票2张"
        ]
    ]
];

// 显示查询结果
echo "查询结果:\n";
echo "总记录数: " . $mockResponse['total_size'] . "\n";
echo "当前页码: " . $mockResponse['page_no'] . "\n";
echo "每页大小: " . $mockResponse['page_size'] . "\n\n";

echo "交易明细:\n";
echo str_repeat("=", 120) . "\n";
printf("%-20s %-25s %-15s %-15s %-10s %-15s %-10s\n", 
    "创建时间", "支付宝订单号", "商户订单号", "商品标题", "金额", "交易状态", "手续费");
echo str_repeat("-", 120) . "\n";

$totalAmount = 0;
$totalFee = 0;
$successCount = 0;

foreach ($mockResponse['detail_list'] as $transaction) {
    printf("%-20s %-25s %-15s %-15s %-10s %-15s %-10s\n",
        $transaction['gmt_create'],
        $transaction['alipay_order_no'],
        $transaction['merchant_order_no'],
        mb_substr($transaction['goods_title'], 0, 12) . '...',
        $transaction['total_amount'],
        $transaction['trade_status'],
        $transaction['service_fee']
    );
    
    if ($transaction['trade_status'] === '交易成功') {
        $totalAmount += floatval($transaction['total_amount']);
        $totalFee += floatval($transaction['service_fee']);
        $successCount++;
    }
}

echo str_repeat("=", 120) . "\n";
echo "\n统计信息:\n";
echo "成功交易笔数: {$successCount}\n";
echo "成功交易总额: ¥" . number_format($totalAmount, 2) . "\n";
echo "总手续费: ¥" . number_format($totalFee, 2) . "\n";
echo "平均交易金额: ¥" . number_format($totalAmount / max($successCount, 1), 2) . "\n";

echo "\n=== 查询完成 ===\n";
?>
