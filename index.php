<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝账单查询系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #1677ff;
            margin-bottom: 30px;
        }
        .query-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            background: #1677ff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0958d9;
        }
        .results {
            margin-top: 30px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-success {
            color: #52c41a;
            font-weight: bold;
        }
        .status-closed {
            color: #ff4d4f;
            font-weight: bold;
        }
        .amount {
            font-weight: bold;
            color: #fa8c16;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 支付宝账单查询系统</h1>
            <p>查询和分析您的支付宝交易数据</p>
        </div>

        <div class="query-form">
            <h3>📅 查询条件</h3>
            <form method="GET">
                <div class="form-group">
                    <label>查询日期:</label>
                    <input type="date" name="date" value="<?php echo $_GET['date'] ?? '2025-05-30'; ?>">
                </div>
                <div class="form-group">
                    <label>页面大小:</label>
                    <select name="page_size">
                        <option value="10" <?php echo ($_GET['page_size'] ?? '') == '10' ? 'selected' : ''; ?>>10条</option>
                        <option value="50" <?php echo ($_GET['page_size'] ?? '') == '50' ? 'selected' : ''; ?>>50条</option>
                        <option value="100" <?php echo ($_GET['page_size'] ?? '100') == '100' ? 'selected' : ''; ?>>100条</option>
                    </select>
                </div>
                <button type="submit" class="btn">🔍 查询数据</button>
            </form>
        </div>

        <?php
        // 处理查询请求
        $queryDate = $_GET['date'] ?? '2025-05-30';
        $pageSize = $_GET['page_size'] ?? '100';
        
        if ($_GET) {
            // 模拟查询数据
            $mockData = [
                [
                    "gmt_create" => "$queryDate 08:30:15",
                    "alipay_order_no" => "2025053088001234567890123456",
                    "merchant_order_no" => "TX{$queryDate}001",
                    "goods_title" => "在线商城购物",
                    "total_amount" => "299.00",
                    "service_fee" => "2.99",
                    "trade_status" => "交易成功",
                    "store_name" => "线上旗舰店"
                ],
                [
                    "gmt_create" => "$queryDate 10:15:32",
                    "alipay_order_no" => "2025053088001234567890123457",
                    "merchant_order_no" => "TX{$queryDate}002",
                    "goods_title" => "餐饮外卖订单",
                    "total_amount" => "68.50",
                    "service_fee" => "0.69",
                    "trade_status" => "交易成功",
                    "store_name" => "美食广场"
                ],
                [
                    "gmt_create" => "$queryDate 14:22:18",
                    "alipay_order_no" => "2025053088001234567890123458",
                    "merchant_order_no" => "TX{$queryDate}003",
                    "goods_title" => "服装购买",
                    "total_amount" => "158.00",
                    "service_fee" => "1.58",
                    "trade_status" => "交易关闭",
                    "store_name" => "时尚服饰店"
                ],
                [
                    "gmt_create" => "$queryDate 16:45:12",
                    "alipay_order_no" => "2025053088001234567890123459",
                    "merchant_order_no" => "TX{$queryDate}004",
                    "goods_title" => "生活用品",
                    "total_amount" => "89.90",
                    "service_fee" => "0.90",
                    "trade_status" => "交易成功",
                    "store_name" => "日用百货"
                ],
                [
                    "gmt_create" => "$queryDate 19:30:45",
                    "alipay_order_no" => "2025053088001234567890123460",
                    "merchant_order_no" => "TX{$queryDate}005",
                    "goods_title" => "电影票购买",
                    "total_amount" => "45.00",
                    "service_fee" => "0.45",
                    "trade_status" => "交易成功",
                    "store_name" => "影城票务"
                ]
            ];

            // 计算统计数据
            $totalAmount = 0;
            $totalFee = 0;
            $successCount = 0;
            $totalCount = count($mockData);

            foreach ($mockData as $item) {
                if ($item['trade_status'] === '交易成功') {
                    $totalAmount += floatval($item['total_amount']);
                    $totalFee += floatval($item['service_fee']);
                    $successCount++;
                }
            }
        ?>

        <div class="results">
            <h3>📊 查询结果 - <?php echo $queryDate; ?></h3>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value"><?php echo $totalCount; ?></div>
                    <div class="stat-label">总交易笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value"><?php echo $successCount; ?></div>
                    <div class="stat-label">成功交易笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">¥<?php echo number_format($totalAmount, 2); ?></div>
                    <div class="stat-label">成功交易总额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">¥<?php echo number_format($totalFee, 2); ?></div>
                    <div class="stat-label">总手续费</div>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>创建时间</th>
                        <th>支付宝订单号</th>
                        <th>商户订单号</th>
                        <th>商品标题</th>
                        <th>交易金额</th>
                        <th>手续费</th>
                        <th>交易状态</th>
                        <th>店铺名称</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($mockData as $item): ?>
                    <tr>
                        <td><?php echo $item['gmt_create']; ?></td>
                        <td><?php echo $item['alipay_order_no']; ?></td>
                        <td><?php echo $item['merchant_order_no']; ?></td>
                        <td><?php echo $item['goods_title']; ?></td>
                        <td class="amount">¥<?php echo $item['total_amount']; ?></td>
                        <td>¥<?php echo $item['service_fee']; ?></td>
                        <td class="<?php echo $item['trade_status'] === '交易成功' ? 'status-success' : 'status-closed'; ?>">
                            <?php echo $item['trade_status']; ?>
                        </td>
                        <td><?php echo $item['store_name']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php } else { ?>
        <div class="results">
            <p style="text-align: center; color: #666; font-size: 16px;">
                👆 请选择查询条件并点击"查询数据"按钮开始查询
            </p>
        </div>
        <?php } ?>
    </div>
</body>
</html>
