<?php

use Alipay\OpenAPISDK\Api\AlipayDataBillBuyApi;
use Alipay\OpenAPISDK\Util\AlipayConfigUtil;
use Alipay\OpenAPISDK\Util\Model\AlipayConfig;
use Alipay\OpenAPISDK\Util\Model\CustomizedParams;
use GuzzleHttp\Client;
use Alipay\OpenAPISDK\ApiException;
use Alipay\OpenAPISDK\Model\AlipayDataBillBuyQueryDefaultResponse;
use Alipay\OpenAPISDK\Model\AlipayDataBillBuyQueryResponseModel;

// 初始化SDK
$alipayConfigUtil = new AlipayConfigUtil(getAlipayConfig());

// 构造请求参数以调用接口
$apiInstance = new AlipayDataBillBuyApi(
  new Client()
);
// 设置AlipayConfigUtil
$apiInstance->setAlipayConfigUtil($alipayConfigUtil);

// 设置交易流水创建时间的起始范围
$startTime = '2019-01-01 00:00:00';
// 设置交易流水创建时间的结束范围
$endTime = '2019-01-02 00:00:00';
// 设置支付宝交易流水号
$alipayOrderNo = '20190101***';
// 设置商户交易号
$merchantOrderNo = 'TX***';
// 设置门店编号
$storeNo = '门店1';
// 设置分页号
$pageNo = '1';
// 设置分页大小1000-2000
$pageSize = '2000';
try {
    $result = $apiInstance->query($startTime, $endTime, $alipayOrderNo, $merchantOrderNo, $storeNo, $pageNo, $pageSize);
    print_r($result);
} catch (ApiException $e) {
    echo '调用失败: ', $e->getMessage(), PHP_EOL;
    echo 'body: ', $e->getResponseBody(), PHP_EOL;
    var_dump('header: ', $e->getResponseHeaders());
}

function getAlipayConfig()
{
    $alipayConfig = new AlipayConfig();
    $alipayConfig->setServerUrl('https://openapi.alipay.com');
    $alipayConfig->setAppId('2021005119698187');
    $alipayConfig->setPrivateKey('<-- 请填写您的应用私钥，例如：MIIEvQIBADANB ... ... -->');
    $alipayConfig->setAlipayPublicKey('<-- 请填写您的支付宝公钥，例如：MIIBIjANBg... -->');
    return $alipayConfig;
}