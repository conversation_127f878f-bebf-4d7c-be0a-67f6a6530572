<?php
require_once 'vendor/autoload.php';

use Alipay\EasySDK\Kernel\Factory;
use Alipay\EasySDK\Kernel\Config;

echo "=== 支付宝真实账单查询系统 ===\n";
echo "查询日期: 2025-05-30\n";
echo "查询时间: " . date('Y-m-d H:i:s') . "\n";
echo "================================\n\n";

// 配置支付宝SDK
$options = new Config();
$options->protocol = 'https';
$options->gatewayHost = 'openapi.alipay.com';
$options->signType = 'RSA2';

// 使用readme.mz中的配置信息
$options->appId = '2021005119698187';

// 应用私钥
$options->merchantPrivateKey = 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCIAuPG6ks9kh07vX9fHTH/+6jNG1glO5/mqIXQKO/g3Dvu+eYFbket9vZbVAO0TQYd/p2Lzp2WZBmfxocHYDumM/cLvVAEW8sDKkTLOWgV06yvucnbalTltHus43KTcx1KvmybN2wJRxOsaRWAEk+awVkibhpKGliNr4b1ah8XYWCrePGAUUXMRj04vXufhGnSe+E6ryUGwul9ZjdYtLx4mznEHLNvPc7Vojv2R514Jwp1b/q3IqPSIArpaGSeUs79i/R1blC1LrSViniVqnLpp0kRkEuS0/hjDPImEcie0QwjWWfHKQqP30dTTh+gSh7v0Iv+yfOOi/PvcBncKZx1AgMBAAECggEALFAEtgIPkXfRXm1W2j5A1A3B6VFHXpoWdqfhMPilbrVSMYHpl0tevyb/DLJKoquVmqAh5DLk1OK4Fn4v8A9CX9v+WSzMrR7a/aT/1NZXOwVD9dyqD3qNPmmXAbT412Fh4cA40jk0UbF+j2WNQ7SzitADolwM5KfAwii1568zggISOoQdkvR9+ERdW0NbY+6nyQj5fPJeQb+PaSU4XUL+YvH38PBFQ21LSdxC4hpR+btYxu8KJ0a5lhXu8FPVEjshYMhUwwGUUXbt/7mer2Yg6X3zN2q6DUXvQXZAtLSUlzQvxDgIyUJAt61v+T3/xaZpRVLRFaSGsNOXUmRluZAQ4QKBgQDxkfJBYMnzg86WMZqFrToB4PwL8RgKFNUkL9LCHLVxK7kfksuYVJsr8BWvc8rrQuCjhjdBhylddUqeGDJ+8I36ti9UZ4vDgb0S1pMYvQRmUZeC37EKTKUjSsu5hICd8mgY3AuRUNJkFAkuFQMSeHSWe0dz2UyBVoM+oYJ1T8k3UwKBgQCQIsEA/dZYXVlfGGSVXhNmy8uTnCC010pQ2jq3rKiK0kxXvGSAooSIuML46hwn2TpJXW5sYPnvmMl4osEBtaGuP8OSlgJ/7ZP56LHCJeY2nBqOZzngyFdZwQBCkig4Dm1HZF53lfRQCks3YHauWMXpYa/dDn+yOgVZcOSgEJNMFwKBgQCDnr2cGZxvbhWViBllVGkStP8fkpFCjO9E9DmlQfcqXmRTa6w6p36Uhg+KtVCOtrWm424f6gEDxvCNCyoYOAFj5PgMyQ5By+K07Ozgwbwv86zVxgO0VOZ1QD+YKTXa2UUWpm43Ew5PMQt/bDtsSO1dQHZCDNe+cOC5s05dlMdRuQKBgQCIsOzoy9IjKyQ+kxuQrA8qRctiyYYa+rF3y/4zgoK0ZIwSCJAnjfiy0MXW2e6pu9ETEpBOKAnft74Zsf/oZyBV6BLJSYpFWEIllxA9V0PkNlbZBfxVuKlebTKZ75JE1ym7suwD7SotXhXHBqyG25mVoxbtRXrEw1GfaPjo8889MQKBgQDeTTXt6cv4PRB9VN981cqmJJUJVMqpI8CDjpzeZE95G3aljeQcfWBSpD1koudYMaTeWxXWLqt4a+Yz8D4/AAf5tZeAYpm7jksaihR4NVjG9w66JKcCXYa69e5yDIx5+Wlq6QAUWQOyFdttbYYIotGmAmJtHlkkZ+VnXRodRsz/Bg==';

// 支付宝公钥
$options->alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+s69Posg9bkvSDge58uVtNaSY0msXaAe4LyJTfDp1mvQKlpeRq01ic+yLnGNoEByosOqC4PG+xMzcahU/+1QD4Lnt5Y9p1uzMozGmE2pE2TZcIoaquW75ylxSYURGYHAJ2X5Xk9y1hVKJxLVeDCEY3HfCa+ymlWguSB8DYWE7mJyFtXWrSZOVzfiV6+m1FKVrfWsFBwf+Din8OKBes1AGMZ2xsVoCl+m4Mp9d1j8cbH0OwKTwNUJsOJLEKAxmy6Nkhl+6/fJu7tIAvQn76fi/oTYu71XWAfpoex1TehETBZ/6bNqSr3ztZLZF1fGGpcTr6gWan1Vye6VTzRApnvDwIDAQAB';

// 初始化SDK
Factory::setOptions($options);

echo "正在连接支付宝开放平台...\n";

try {
    // 使用通用API调用方式来调用账单查询接口
    $response = Factory::util()->generic()->execute(
        'alipay.data.bill.buy.query',
        [],
        [
            'start_time' => '2025-05-30 00:00:00',
            'end_time' => '2025-05-31 00:00:00',
            'page_no' => '1',
            'page_size' => '2000'
        ]
    );

    echo "API调用成功！\n";
    echo "响应数据:\n";
    echo "================================\n";

    // 解析响应 - response是一个对象，需要获取其属性
    echo "响应对象类型: " . get_class($response) . "\n";

    // 从响应对象中获取httpBody
    $responseBody = $response->httpBody;
    echo "获取到响应体数据\n";
    $responseArray = json_decode($responseBody, true);
    
    if (isset($responseArray['alipay_data_bill_buy_query_response'])) {
        $billData = $responseArray['alipay_data_bill_buy_query_response'];
        
        if (isset($billData['code']) && $billData['code'] === '10000') {
            echo "查询成功！\n\n";
            
            // 显示基本信息
            if (isset($billData['page_no'])) {
                echo "页码: " . $billData['page_no'] . "\n";
            }
            if (isset($billData['page_size'])) {
                echo "每页大小: " . $billData['page_size'] . "\n";
            }
            if (isset($billData['total_size'])) {
                echo "总记录数: " . $billData['total_size'] . "\n";
            }
            
            // 显示详细数据
            if (isset($billData['detail_list']) && is_array($billData['detail_list'])) {
                echo "\n交易明细:\n";
                echo str_repeat("=", 100) . "\n";
                
                foreach ($billData['detail_list'] as $index => $transaction) {
                    echo "交易 " . ($index + 1) . ":\n";
                    foreach ($transaction as $key => $value) {
                        echo "  {$key}: {$value}\n";
                    }
                    echo str_repeat("-", 50) . "\n";
                }
            } else {
                echo "\n暂无交易记录\n";
            }
            
        } else {
            echo "查询失败！\n";
            echo "错误代码: " . ($billData['code'] ?? 'unknown') . "\n";
            echo "错误信息: " . ($billData['msg'] ?? 'unknown') . "\n";
            echo "子错误代码: " . ($billData['sub_code'] ?? 'none') . "\n";
            echo "子错误信息: " . ($billData['sub_msg'] ?? 'none') . "\n";
        }
    } else {
        echo "响应格式异常\n";
        echo "完整响应: " . json_encode($responseArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }

} catch (Exception $e) {
    echo "调用失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "\n=== 查询完成 ===\n";
?>
